{"name": "social-travel-trip-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 4200 --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@clerk/localizations": "^3.14.4", "@clerk/nextjs": "^6.18.5", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@photo-sphere-viewer/core": "^5.13.2", "@photo-sphere-viewer/markers-plugin": "^5.13.2", "@react-oauth/google": "^0.12.2", "@tanstack/react-query": "^5.76.1", "antd": "^5.25.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "emoji-picker-react": "^4.12.2", "leaflet": "^1.9.4", "lucide-react": "^0.507.0", "next": "15.3.1", "next-themes": "^0.4.6", "photo-sphere-viewer": "^4.8.1", "radix-ui": "^1.3.4", "react": "^19.1.0", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.2", "react-leaflet": "^5.0.0", "react-leaflet-markercluster": "^5.0.0-rc.0", "react-map-gl": "^8.0.4", "react-markdown": "^10.1.0", "react-photo-sphere-viewer": "^6.2.3", "rxjs": "^7.8.2", "sonner": "^2.0.3", "tailwind-merge": "^2.5.2", "tailwindcss": "^4.1.6", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.6", "@types/leaflet": "^1.9.17", "@types/node": "^20", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "typescript": "^5"}}