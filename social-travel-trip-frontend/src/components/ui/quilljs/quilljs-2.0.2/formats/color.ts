import { ClassAttributor, Scope, StyleAttributor } from '../lib/parchment-3.0.0/parchment';

class ColorAttributor extends StyleAttributor {
  value(domNode: HTMLElement) {
    let value = super.value(domNode) as string;
    if (!value.startsWith('rgb(')) return value;
    value = value.replace(/^[^\d]+/, '').replace(/[^\d]+$/, '');
    const hex = value
      .split(',')
      .map((component) => `00${parseInt(component, 10).toString(16)}`.slice(-2))
      .join('');
    return `#${hex}`;
  }
}

const ColorClass = new ClassAttributor('color', 'ql-color', {
  scope: Scope.INLINE,
});
const ColorStyle = new ColorAttributor('color', 'color', {
  scope: Scope.INLINE,
});

export { ColorAttributor, ColorClass, ColorStyle };
