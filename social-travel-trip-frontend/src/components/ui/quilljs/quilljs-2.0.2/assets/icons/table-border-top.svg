<svg viewbox="0 0 18 18">
  <g class="ql-fill ql-transparent">
    <rect height="2" transform="translate(-12 18) rotate(-90)" width="2" x="2" y="14"></rect>
    <rect height="2" transform="translate(-9 15) rotate(-90)" width="2" x="2" y="11"></rect>
    <rect height="2" transform="translate(-6 12) rotate(-90)" width="2" x="2" y="8"></rect>
    <rect height="2" transform="translate(0 6) rotate(-90)" width="2" x="2" y="2"></rect>
    <rect height="2" transform="translate(-3 9) rotate(-90)" width="2" x="2" y="5"></rect>
    <rect height="2" transform="translate(0 30) rotate(-90)" width="2" x="14" y="14"></rect>
    <rect height="2" transform="translate(3 27) rotate(-90)" width="2" x="14" y="11"></rect>
    <rect height="2" transform="translate(6 24) rotate(-90)" width="2" x="14" y="8"></rect>
    <rect height="2" transform="translate(12 18) rotate(-90)" width="2" x="14" y="2"></rect>
    <rect height="2" transform="translate(9 21) rotate(-90)" width="2" x="14" y="5"></rect>
  </g>
  <line class="ql-stroke-miter" x1="16" x2="2" y1="3" y2="3"></line>
  <g class="ql-fill ql-transparent">
    <rect height="2" transform="translate(24 30) rotate(-180)" width="2" x="11" y="14"></rect>
    <rect height="2" transform="translate(18 30) rotate(-180)" width="2" x="8" y="14"></rect>
    <rect height="2" transform="translate(12 30) rotate(-180)" width="2" x="5" y="14"></rect>
    <rect height="2" transform="translate(24 6) rotate(-180)" width="2" x="11" y="2"></rect>
    <rect height="2" transform="translate(18 6) rotate(-180)" width="2" x="8" y="2"></rect>
    <rect height="2" transform="translate(18 12) rotate(-180)" width="2" x="8" y="5"></rect>
    <rect height="2" transform="translate(18 18) rotate(-180)" width="2" x="8" y="8"></rect>
    <rect height="2" transform="translate(18 24) rotate(-180)" width="2" x="8" y="11"></rect>
    <rect height="2" transform="translate(3 21) rotate(-90)" width="2" x="11" y="8"></rect>
    <rect height="2" transform="translate(-3 15) rotate(-90)" width="2" x="5" y="8"></rect>
    <rect height="2" transform="translate(12 6) rotate(-180)" width="2" x="5" y="2"></rect>
  </g>
</svg>
