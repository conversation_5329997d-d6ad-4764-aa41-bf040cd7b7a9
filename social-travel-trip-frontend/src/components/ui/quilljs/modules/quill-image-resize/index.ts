import defaultsDeep from 'lodash/defaultsDeep';
import Quill from '../../quilljs-2.0.2/quill';
import DefaultOptions from './DefaultOptions';
import { DisplaySize } from './modules/DisplaySize';
import { Resize } from './modules/Resize';
import Emitter from '../../quilljs-2.0.2/core/emitter';

const knownModules = { DisplaySize, Resize };

/**
 * Custom module for quilljs to allow user to resize <img> elements
 * (Works on Chrome, Edge, Safari and replaces Firefox's native resize behavior)
 * @see https://quilljs.com/blog/building-a-custom-module/
 */
class ImageResize {
  quill: Quill;
  options: any;
  modules: any[];
  moduleClasses: any[];
  // @ts-expect-error
  img: HTMLElement;
  overlay: any;

  // @ts-expect-error
  constructor(quill, options: any = {}) {
    // save the quill reference and options
    this.quill = quill;

    // Apply the options to our defaults, and stash them for later
    // defaultsDeep doesn't do arrays as you'd expect, so we'll need to apply the classes array from options separately
    let moduleClasses = false;
    if (options.modules) {
      moduleClasses = options.modules.slice();
    }

    // Apply options to default options
    this.options = defaultsDeep({}, options, DefaultOptions);

    // (see above about moduleClasses)
    if (moduleClasses !== false) {
      this.options.modules = moduleClasses;
    }

    // disable native image resizing on firefox
    document.execCommand('enableObjectResizing', false, 'false');

    // respond to clicks inside the editor
    this.quill.root.addEventListener('click', this.handleClick, false);
    const parentNode = this.quill.root.parentNode as HTMLElement;
    parentNode.style.position = parentNode.style.position || 'relative';

    // setup modules
    this.moduleClasses = this.options.modules;

    this.modules = [];
    //Listen Quill change
    this.listenQuillChange();
  }
  listenQuillChange = () => {
    /**
     * Events
     */
    this.quill.on(Emitter.events.TEXT_CHANGE, (delta, oldDelta, source) => {
      this.onUpdate();
    });
  };

  initializeModules = () => {
    this.removeModules();
    const quillOptions = { icons: ImageResize.Quill.icons };
    this.modules = this.moduleClasses.map(
      // @ts-expect-error
      (ModuleClass) => new (knownModules[ModuleClass] || ModuleClass)(this, quillOptions),
    );

    this.modules.forEach((module) => {
      module.onCreate();
    });

    this.onUpdate();
    /**
     * Windows resize
     */
    window.addEventListener('resize', () => {
      this.onUpdate();
    });
  };

  onUpdate = () => {
    this.repositionElements();
    this.modules.forEach((module) => {
      module.onUpdate();
    });
  };

  removeModules = () => {
    this.modules.forEach((module) => {
      module.onDestroy();
    });

    this.modules = [];
  };

  // @ts-expect-error
  handleClick = (evt) => {
    if (evt.target && evt.target.tagName && evt.target.tagName.toUpperCase() === 'IMG') {
      if (this.img === evt.target) {
        // we are already focused on this image
        return;
      }
      if (this.img) {
        // we were just focused on another image
        this.hide();
      }
      // clicked on an image inside the editor
      this.show(evt.target);
    } else if (this.img) {
      // clicked on a non image
      this.hide();
    }
  };

  show = (img: any) => {
    // keep track of this img element
    this.img = img;

    this.showOverlay();

    this.initializeModules();
  };

  showOverlay = () => {
    if (this.overlay) {
      this.hideOverlay();
    }
    const imgBlot = this.findImgBlot();
    const index = this.quill.getIndex(imgBlot);
    this.quill.setSelection(index);

    // prevent spurious text selection
    this.setUserSelect('none');

    // listen for the image being deleted or moved
    document.addEventListener('keyup', this.checkImage, true);
    this.quill.root.addEventListener('input', this.checkImage, true);

    // Create and add the overlay
    this.overlay = document.createElement('div');
    Object.assign(this.overlay.style, this.options.overlayStyles);

    // @ts-expect-error
    this.quill.root.parentNode.appendChild(this.overlay);

    this.repositionElements();
  };

  hideOverlay = () => {
    if (!this.overlay) {
      return;
    }

    // Remove the overlay
    // @ts-expect-error
    this.quill.root.parentNode.removeChild(this.overlay);
    this.overlay = undefined;

    // stop listening for image deletion or movement
    document.removeEventListener('keyup', this.checkImage);
    this.quill.root.removeEventListener('input', this.checkImage);

    // reset user-select
    this.setUserSelect('');
  };

  repositionElements = () => {
    if (!this.overlay || !this.img) {
      return;
    }

    // position the overlay over the image
    const parent = this.quill.root.parentNode as HTMLElement;
    const imgRect = this.img.getBoundingClientRect();
    const containerRect = parent.getBoundingClientRect();

    Object.assign(this.overlay.style, {
      left: `${imgRect.left - containerRect.left - 1 + parent.scrollLeft}px`,
      top: `${imgRect.top - containerRect.top + parent.scrollTop}px`,
      width: `${imgRect.width}px`,
      height: `${imgRect.height}px`,
    });
  };

  hide = () => {
    this.hideOverlay();
    this.removeModules();
    // @ts-expect-error
    this.img = undefined;
  };

  // @ts-expect-error
  setUserSelect = (value) => {
    ['userSelect', 'mozUserSelect', 'webkitUserSelect', 'msUserSelect'].forEach((prop) => {
      // set on contenteditable element and <html>
      // @ts-expect-error
      this.quill.root.style[prop] = value;
      // @ts-expect-error
      document.documentElement.style[prop] = value;
    });
  };

  // @ts-expect-error
  checkImage = (evt) => {
    if (this.img) {
      // user nhan backsapace
      if (evt.keyCode == 8) {
        const imgBlot = this.findImgBlot();
        imgBlot.deleteAt(0);
      }
      // khi nhan delete
      if (evt.keyCode == 46) {
        console.log('User remove image by detete key press');
      }
      this.hide();
    }
  };
  findImgBlot = () => ImageResize.Quill.find(this.img);
  //Quill
  static Quill: any = {};
}
//Export
export default ImageResize;
