// Mock data for forum features

// Popular locations in Vietnam
export const POPULAR_LOCATIONS = [
  { id: '1', name: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>' },
  { id: '2', name: '<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>' },
  { id: '3', name: '<PERSON><PERSON>' },
  { id: '4', name: '<PERSON><PERSON><PERSON> <PERSON>' },
  { id: '5', name: '<PERSON><PERSON><PERSON>, Quảng Nam' },
  { id: '6', name: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>' },
  { id: '7', name: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>' },
  { id: '8', name: '<PERSON><PERSON>, <PERSON>h<PERSON><PERSON>' },
  { id: '9', name: '<PERSON><PERSON>, Quảng Ninh' },
  { id: '10', name: '<PERSON><PERSON> Nẵng' },
  { id: '11', name: '<PERSON><PERSON>, <PERSON><PERSON><PERSON> Đ<PERSON>nh' },
  { id: '12', name: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>hu<PERSON>' },
];

// Users for @mention feature
export const USERS = [
  { id: '1', name: '<PERSON><PERSON><PERSON><PERSON>', avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
  { id: '2', name: 'Trần Thu Hà', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
  { id: '3', name: 'Lê Hoàng', avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
  { id: '4', name: 'Ngọc Mai', avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
  { id: '5', name: 'Phạm Tuấn', avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
  { id: '6', name: 'Hoàng Anh', avatar: 'https://images.pexels.com/photos/91227/pexels-photo-91227.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
  { id: '7', name: 'Minh Tâm', avatar: 'https://images.pexels.com/photos/1858175/pexels-photo-1858175.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },
];

// Popular hashtags
export const POPULAR_HASHTAGS = [
  { tag: 'DuLichVietNam', posts: 1205 },
  { tag: 'PhuQuoc', posts: 954 },
  { tag: 'DaNang', posts: 862 },
  { tag: 'SaPa', posts: 743 },
  { tag: 'HaLong', posts: 628 },
  { tag: 'DaLat', posts: 587 },
  { tag: 'HoiAn', posts: 521 },
  { tag: 'NhaTrang', posts: 498 },
  { tag: 'HaNoi', posts: 476 },
  { tag: 'MienTay', posts: 412 },
  { tag: 'PhuotThu', posts: 387 },
  { tag: 'DuLichBien', posts: 356 },
];

// Reaction types
export const REACTION_TYPES = [
  { id: 'like', icon: '👍', label: 'Thích' },
  { id: 'love', icon: '❤️', label: 'Yêu thích' },
  { id: 'haha', icon: '😄', label: 'Haha' },
  { id: 'wow', icon: '😮', label: 'Wow' },
  { id: 'sad', icon: '😢', label: 'Buồn' },
];

// Sample images for image upload preview
export const SAMPLE_IMAGES = [
  'https://images.pexels.com/photos/5746250/pexels-photo-5746250.jpeg?auto=compress&cs=tinysrgb&w=600',
  'https://images.pexels.com/photos/5746242/pexels-photo-5746242.jpeg?auto=compress&cs=tinysrgb&w=600',
  'https://images.pexels.com/photos/3935702/pexels-photo-3935702.jpeg?auto=compress&cs=tinysrgb&w=600',
  'https://images.pexels.com/photos/6271625/pexels-photo-6271625.jpeg?auto=compress&cs=tinysrgb&w=600',
  'https://images.pexels.com/photos/4428272/pexels-photo-4428272.jpeg?auto=compress&cs=tinysrgb&w=600',
  'https://images.pexels.com/photos/4350383/pexels-photo-4350383.jpeg?auto=compress&cs=tinysrgb&w=600',
];
