'use client';

import { ReactNode } from 'react';
import { ThemeProvider } from 'next-themes';
import { Clerk<PERSON>rovider } from '@clerk/nextjs';
import { viVN } from '@clerk/localizations';

export function ClerkProviders({ children }: { children: ReactNode }) {
  return (
    <ClerkProvider 
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      localization={viVN}
    >
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </ClerkProvider>
  );
}